package com.kedish.xyhelper_fox.component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.codec.Codec;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ChatGptSessionAccessComponent {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    private static final Cache<String, Integer> CAR_USE_COUNT_MAP = CacheBuilder.newBuilder()
            .expireAfterWrite(Duration.ofDays(7))
            .build();


    private static final String CHAT_GPT_SESSION_PREFIX = "chatgpt:session_access:";

    public Map<String, Integer> getByCarIds(List<String> carIds) throws ExecutionException {
        if (CollectionUtils.isEmpty(carIds)) {
            return Collections.EMPTY_MAP;
        }
        Map<String, Integer> tempMap = new HashMap<>();
        for (String carId : carIds) {
            Integer count = CAR_USE_COUNT_MAP.get(carId, () -> 0);
            tempMap.put(carId, count);
        }
        return tempMap;
    }


    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void refreshCarUseCount() {
        log.info("开始刷新车队使用次数...");
        Set<String> carIds = CAR_USE_COUNT_MAP.asMap().keySet();

        if (CollectionUtils.isEmpty(carIds)) {
            return;
        }
        try {
            Map<String, Integer> accessCountMap = getAccessCountMap(new ArrayList<>(carIds));
            for (Map.Entry<String, Integer> entry : accessCountMap.entrySet()) {
                String carId = entry.getKey();
                Integer count = entry.getValue();
                CAR_USE_COUNT_MAP.put(carId, count);
            }
        } catch (ExecutionException | InterruptedException e) {
            log.error("刷新车队使用次数异常", e);
        }
        log.info("刷新车队使用次数完成...");
    }

    public void conversation(String carid) {
        RScoredSortedSet<Long> accessSet = redissonClient.getScoredSortedSet(CHAT_GPT_SESSION_PREFIX + carid);
        long currentTime = Instant.now().toEpochMilli();
        // 将当前时间戳作为分数存入有序集合
        accessSet.add(currentTime, currentTime);

        // 删除超过一小时的记录
        long oneHourAgo = Instant.now().minusSeconds(3600).toEpochMilli();
        accessSet.removeRangeByScore(0, true, oneHourAgo, false);
        accessSet.expire(Duration.ofHours(24L));
    }

    private static final String DAILY_MODEL_STATS_PREFIX = "chatgpt:daily:model:";
    private static final String DAILY_USER_STATS_PREFIX = "chatgpt:daily:user:";

    public void conversation(String carid, String userToken, String model) {

        
        threadPoolExecutor.execute(() -> {
            try {
                // 调用原有的carid统计方法
                if (carid != null && !carid.isEmpty()) {

                    conversation(carid);
                }

                // 获取当前日期作为key的一部分
                String today = java.time.LocalDate.now().toString();
                // 更新模型维度的统计
                String modelKey = DAILY_MODEL_STATS_PREFIX + today;
                RMap<String, Integer> modelStats = redissonClient.getMap(modelKey, JsonJacksonCodec.INSTANCE);
                modelStats.addAndGet(model, 1);
                // 设置24小时后过期
                modelStats.expire(Duration.ofHours(48));

                // 更新用户维度的统计
                String userKey = DAILY_USER_STATS_PREFIX + today;
                RMap<String, Integer> userStats = redissonClient.getMap(userKey,JsonJacksonCodec.INSTANCE);
                userStats.addAndGet(userToken, 1);
                // 设置24小时后过期
                userStats.expire(Duration.ofHours(48));
            } catch (Exception e) {
                log.error("更新每日统计数据失败", e);
            }
        });
    }

    /**
     * 获取指定日期的模型使用统计
     * @param date 日期，格式：yyyy-MM-dd
     * @return 模型使用统计map
     */
    public Map<String, Integer> getDailyModelStats(String date) {
        String key = DAILY_MODEL_STATS_PREFIX + date;
        RMap<String, Integer> modelStats = redissonClient.getMap(key,JsonJacksonCodec.INSTANCE);
        return new HashMap<>(modelStats.readAllMap());
    }

    /**
     * 获取指定日期的用户使用统计
     * @param date 日期，格式：yyyy-MM-dd
     * @return 用户使用统计map
     */
    public Map<String, Integer> getDailyUserStats(String date) {
        String key = DAILY_USER_STATS_PREFIX + date;
        RMap<String, Integer> userStats = redissonClient.getMap(key,JsonJacksonCodec.INSTANCE);
        return new HashMap<>(userStats.readAllMap());
    }

    // 获取过去一小时内的访问次数
    public int getAccessCount(String carid) {
        RScoredSortedSet<Long> accessSet = redissonClient.getScoredSortedSet(CHAT_GPT_SESSION_PREFIX + carid);


        long oneHourAgo = Instant.now().minusSeconds(3600).toEpochMilli();
        long currentTime = Instant.now().toEpochMilli();

        // 统计一小时内的访问次数
        return accessSet.count(oneHourAgo, true, currentTime, true);
    }

    public Map<String, Integer> getAccessCountMap(List<String> carIds) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isEmpty(carIds)) {
            return null;
        }
        RBatch batch = redissonClient.createBatch();
        Map<String, RFuture<Integer>> accessSetMap = new HashMap<>();

        long oneHourAgo = Instant.now().minusSeconds(3600).toEpochMilli();
        long currentTime = Instant.now().toEpochMilli();

        // 为每个设备的访问集合创建异步计数操作
        for (String carid : carIds) {
            String redisKey = CHAT_GPT_SESSION_PREFIX + carid;
            RScoredSortedSetAsync<Long> accessSet = batch.getScoredSortedSet(redisKey);
            RFuture<Integer> integerRFuture = accessSet.countAsync(oneHourAgo, true, currentTime, true);// 异步计数
            accessSetMap.put(carid, integerRFuture);
        }

        // 执行批处理并获取结果
        batch.execute();

        // 收集所有设备的访问计数
        Map<String, Integer> results = new HashMap<>();
        for (Map.Entry<String, RFuture<Integer>> entry : accessSetMap.entrySet()) {
            results.put(entry.getKey(), entry.getValue().get());
        }

        return results;
    }

    /**
     * 统计在线用户数量
     * @return 在线用户数量
     */
    public long getOnlineUserCount() {
        try {
            // 使用 SCAN 命令统计 gfsession 开头的key数量
            String script = """
                local cursor = '0'
                local count = 0
                repeat
                    local result = redis.call('SCAN', cursor, 'MATCH', 'gfsession*', 'COUNT', 1000)
                    cursor = result[1]
                    count = count + #result[2]
                until cursor == '0'
                return count
                """;
            
            return redissonClient.getScript().eval(RScript.Mode.READ_ONLY, script, RScript.ReturnType.INTEGER);
        } catch (Exception e) {
            log.error("统计在线用户数量失败", e);
            return 0;
        }
    }
}
